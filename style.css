/* Enhanced fonts for better typography */
@import url('https://fonts.googleapis.com/css2?family=Quicksand:wght@400;700&family=Pacifico&family=Dancing+Script:wght@400;700&family=Crimson+Text:ital,wght@0,400;0,600;1,400&family=Playfair+Display:ital,wght@0,400;0,600;1,400&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Quicksand', 'Poppins', sans-serif;
    background: linear-gradient(135deg, #fef7f0 0%, #fce4ec 50%, #f8bbd9 100%);
    background-size: 400% 400%;
    animation: gradientMove 12s ease-in-out infinite;
    height: 100vh;
    overflow: hidden;
    position: relative;
    margin: 0;
    padding: 0;
}

@keyframes gradientMove {
    0% {background-position: 0% 50%;}
    25% {background-position: 100% 50%;}
    50% {background-position: 100% 100%;}
    75% {background-position: 0% 100%;}
    100% {background-position: 0% 50%;}
}

/* Container chính - fullscreen */
.main-container {
    height: 100vh;
    width: 100vw;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding: 20px;
}

/* Triangular bunting flags */
.bunting-flags {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 80px;
    z-index: 5;
    display: flex;
    justify-content: space-around;
    align-items: flex-start;
    padding-top: 10px;
}

.flag {
    width: 0;
    height: 0;
    border-left: 25px solid transparent;
    border-right: 25px solid transparent;
    border-top: 40px solid #ff6b9d;
    position: relative;
    animation: flagWave 3s ease-in-out infinite;
}

.flag:nth-child(even) {
    border-top-color: #ff8fab;
    animation-delay: 0.5s;
}

.flag:nth-child(3n) {
    border-top-color: #ffa8cc;
    animation-delay: 1s;
}

@keyframes flagWave {
    0%, 100% { transform: rotate(0deg) translateY(0); }
    50% { transform: rotate(2deg) translateY(-5px); }
}

/* Main birthday layout */
.birthday-layout {
    display: flex;
    width: 100%;
    max-width: 1200px;
    height: 100%;
    align-items: center;
    justify-content: center;
    gap: 80px;
    z-index: 10;
    padding: 0 40px;
}

/* Trang trí nền */
.background-decorations {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

/* Bong bóng trang trí */
.bubbles {
    position: absolute;
    width: 100%;
    height: 100%;
}

.bubble {
    position: absolute;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: bubbleFloat 8s ease-in-out infinite;
}

.bubble-1 {
    width: 40px;
    height: 40px;
    left: 10%;
    animation-delay: 0s;
    animation-duration: 10s;
}

.bubble-2 {
    width: 30px;
    height: 30px;
    left: 20%;
    animation-delay: 2s;
    animation-duration: 12s;
}

.bubble-3 {
    width: 50px;
    height: 50px;
    left: 30%;
    animation-delay: 4s;
    animation-duration: 8s;
}

.bubble-4 {
    width: 35px;
    height: 35px;
    left: 70%;
    animation-delay: 1s;
    animation-duration: 11s;
}

.bubble-5 {
    width: 45px;
    height: 45px;
    left: 80%;
    animation-delay: 3s;
    animation-duration: 9s;
}

.bubble-6 {
    width: 25px;
    height: 25px;
    left: 90%;
    animation-delay: 5s;
    animation-duration: 13s;
}

@keyframes bubbleFloat {
    0%, 100% {
        transform: translateY(100vh) translateX(0) scale(1);
        opacity: 0;
    }
    10% {
        opacity: 0.7;
    }
    90% {
        opacity: 0.7;
    }
    100% {
        transform: translateY(-100px) translateX(50px) scale(1.2);
        opacity: 0;
    }
}

/* Hoa trang trí */
.flowers {
    position: absolute;
    width: 100%;
    height: 100%;
}

.flower {
    position: absolute;
    font-size: 2rem;
    animation: flowerSway 6s ease-in-out infinite;
}

.flower-1 {
    top: 15%;
    left: 5%;
    animation-delay: 0s;
}

.flower-2 {
    top: 25%;
    right: 10%;
    animation-delay: 1.5s;
}

.flower-3 {
    bottom: 30%;
    left: 15%;
    animation-delay: 3s;
}

.flower-4 {
    bottom: 20%;
    right: 5%;
    animation-delay: 4.5s;
}

@keyframes flowerSway {
    0%, 100% {
        transform: rotate(0deg) translateY(0);
    }
    25% {
        transform: rotate(5deg) translateY(-10px);
    }
    50% {
        transform: rotate(-3deg) translateY(-5px);
    }
    75% {
        transform: rotate(8deg) translateY(-15px);
    }
}

/* Ảnh trôi như bong bóng */
.floating-images {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

.floating-img {
    position: absolute;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(255, 255, 255, 0.9);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    opacity: 0.9;
    animation: bubbleImageFloat 20s ease-in-out infinite;
}

.img-1 {
    top: 15%;
    left: 10%;
    animation-delay: 0s;
    animation-duration: 25s;
}

.img-2 {
    top: 60%;
    right: 15%;
    animation-delay: 8s;
    animation-duration: 22s;
}

.img-3 {
    bottom: 20%;
    left: 20%;
    animation-delay: 15s;
    animation-duration: 28s;
}

@keyframes bubbleImageFloat {
    0%, 100% {
        transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
        opacity: 0.9;
    }
    25% {
        transform: translateY(-60px) translateX(40px) rotate(10deg) scale(1.1);
        opacity: 1;
    }
    50% {
        transform: translateY(-30px) translateX(-30px) rotate(-8deg) scale(0.95);
        opacity: 0.95;
    }
    75% {
        transform: translateY(-80px) translateX(20px) rotate(15deg) scale(1.05);
        opacity: 1;
    }
}

/* Left section styling */
.left-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    gap: 35px;
    padding-left: 60px;
    position: relative;
    animation: slideInLeft 1.2s ease-out;
}

@keyframes slideInLeft {
    0% { opacity: 0; transform: translateX(-100px); }
    100% { opacity: 1; transform: translateX(0); }
}

.title-with-hat {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.party-hat {
    font-size: 3rem;
    position: absolute;
    top: -20px;
    left: -10px;
    transform: rotate(-15deg);
    animation: hatBounce 2s ease-in-out infinite;
}

@keyframes hatBounce {
    0%, 100% { transform: rotate(-15deg) translateY(0); }
    50% { transform: rotate(-10deg) translateY(-8px); }
}

.main-title {
    font-family: 'Pacifico', cursive;
    font-size: 5.5rem;
    font-weight: bold;
    color: #ff1493;
    margin: 0;
    line-height: 0.85;
    text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.8), 0 0 10px rgba(255, 20, 147, 0.3);
    filter: drop-shadow(2px 2px 4px rgba(255, 107, 157, 0.3));
    animation: titlePop 1.5s cubic-bezier(.68,-0.55,.27,1.55);
    position: relative;
    z-index: 10;
}

.main-title:first-of-type {
    animation-delay: 0.2s;
}

.main-title:last-of-type {
    animation-delay: 0.4s;
}

@keyframes titlePop {
    0% { opacity: 0; transform: scale(0.5) translateY(50px); }
    100% { opacity: 1; transform: scale(1) translateY(0); }
}



/* Date banner styling */
.date-banner {
    background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%);
    padding: 12px 30px;
    border-radius: 25px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 8px 25px rgba(255, 107, 157, 0.3);
    animation: bannerSlide 1s ease-out 0.8s both;
}

@keyframes bannerSlide {
    0% { opacity: 0; transform: translateX(-100px); }
    100% { opacity: 1; transform: translateX(0); }
}

.star-decoration {
    color: #fff;
    font-size: 1.2rem;
    animation: starTwinkle 2s ease-in-out infinite;
}

.star-decoration:nth-child(odd) {
    animation-delay: 0.5s;
}

.date-text {
    font-family: 'Quicksand', sans-serif;
    font-size: 1.3rem;
    font-weight: 700;
    color: #fff;
    letter-spacing: 2px;
}

/* Interactive buttons styling */
.interactive-buttons {
    display: flex;
    gap: 20px;
    align-items: center;
    animation: buttonFloat 1.2s ease-out 1.2s both;
}

/* Letter button container - enhanced visibility */
.letter-button-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    animation: letterFloat 3s ease-in-out infinite;
    padding: 10px;
    border-radius: 20px;
    background: radial-gradient(circle, rgba(65, 105, 225, 0.05) 0%, transparent 60%);
}

.letter-button-container:hover {
    transform: translateY(-3px) scale(1.05);
}

.letter-button-container:active {
    transform: translateY(-2px) scale(1.02);
}

/* Letter button - clean envelope design */
.letter-button {
    font-size: 3rem;
    transition: all 0.3s ease;
    filter: drop-shadow(0 4px 8px rgba(255, 107, 157, 0.3));
    margin-bottom: 5px;
}

/* Letter image styling - balanced visibility */
.letter-img {
    width: 85px;
    height: 85px;
    transition: all 0.3s ease;
    filter: drop-shadow(0 4px 12px rgba(65, 105, 225, 0.4));
    border-radius: 15px;
    background: linear-gradient(135deg, rgba(230, 243, 255, 0.3), rgba(179, 217, 255, 0.2));
    padding: 8px;
    border: 1px solid rgba(65, 105, 225, 0.2);
    position: relative;
    z-index: 5;
}

.letter-button-container:hover .letter-button {
    transform: scale(1.1);
    filter: drop-shadow(0 8px 16px rgba(255, 107, 157, 0.5));
}

.letter-button-container:hover .letter-img {
    transform: scale(1.08);
    filter: drop-shadow(0 6px 18px rgba(65, 105, 225, 0.5));
}

/* Letter label */
.letter-label {
    font-family: 'Quicksand', sans-serif;
    font-size: 0.9rem;
    font-weight: 700;
    color: #ffffff;
    text-shadow:
        0 2px 4px rgba(255, 107, 157, 0.8),
        0 0 10px rgba(255, 107, 157, 0.6),
        0 0 20px rgba(255, 107, 157, 0.4);
    margin-top: 2px;
    background: linear-gradient(135deg, #ff6b9d, #ff8fab);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

@keyframes letterFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-8px) rotate(2deg); }
}

/* 3D Birthday Cake Design */
.cake-container {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    animation: cakeGlow 3s ease-in-out infinite;
    padding: 10px;
    border-radius: 20px;
    background: radial-gradient(circle, rgba(255, 20, 147, 0.05) 0%, transparent 60%);
}

.birthday-cake-image {
    position: relative;
    width: 85px;
    height: 85px;
    animation: cakeBounce 2s ease-in-out infinite;
    border-radius: 15px;
    background: linear-gradient(135deg, rgba(255, 182, 193, 0.3), rgba(255, 105, 180, 0.2));
    padding: 8px;
    border: 1px solid rgba(255, 20, 147, 0.2);
    z-index: 5;
}

.cake-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    filter: drop-shadow(0 4px 12px rgba(255, 20, 147, 0.4));
    transition: all 0.3s ease;
}

.cake-layer {
    position: absolute;
    border-radius: 50%;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transform-style: preserve-3d;
}

.cake-bottom {
    width: 90px;
    height: 35px;
    background: linear-gradient(135deg, #ff8fab 0%, #ffa8cc 50%, #ff6b9d 100%);
    bottom: 0;
    left: 5px;
    border: 3px solid #fff;
}

.cake-middle {
    width: 70px;
    height: 30px;
    background: linear-gradient(135deg, #ffb6d9 0%, #ffc9e0 50%, #ff8fab 100%);
    bottom: 25px;
    left: 15px;
    border: 2px solid #fff;
}

.cake-top {
    width: 50px;
    height: 25px;
    background: linear-gradient(135deg, #ffc9e0 0%, #ffe0f0 50%, #ffb6d9 100%);
    bottom: 45px;
    left: 25px;
    border: 2px solid #fff;
}

.cake-decoration {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    height: 4px;
    background: linear-gradient(90deg, #fff 0%, #ffe0f0 50%, #fff 100%);
    border-radius: 2px;
}

/* Candles */
.candles {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
}

.candle {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.candle-stick {
    width: 4px;
    height: 20px;
    background: linear-gradient(to bottom, #fff 0%, #ffe0f0 100%);
    border-radius: 2px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.flame {
    width: 6px;
    height: 8px;
    background: radial-gradient(circle, #ffd700 0%, #ff6b35 70%, #ff4500 100%);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    position: absolute;
    top: -8px;
    animation: flameFlicker 0.8s ease-in-out infinite alternate;
}

.flame-1 { animation-delay: 0s; }
.flame-2 { animation-delay: 0.3s; }
.flame-3 { animation-delay: 0.6s; }

@keyframes flameFlicker {
    0% {
        transform: scale(1) rotate(-2deg);
        box-shadow: 0 0 8px #ffd700, 0 0 16px #ff6b35;
    }
    100% {
        transform: scale(1.1) rotate(2deg);
        box-shadow: 0 0 12px #ffd700, 0 0 24px #ff6b35;
    }
}

/* Cake sparkles */
.cake-sparkles {
    position: absolute;
    top: -10px;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.sparkle {
    position: absolute;
    font-size: 1rem;
    animation: sparkleFloat 2s ease-in-out infinite;
}

.sparkle-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.sparkle-2 {
    top: 40%;
    right: 10%;
    animation-delay: 0.7s;
}

.sparkle-3 {
    bottom: 30%;
    left: 20%;
    animation-delay: 1.4s;
}

@keyframes sparkleFloat {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.3) rotate(180deg);
        opacity: 1;
    }
}

@keyframes cakeBounce {
    0%, 100% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-10px) scale(1.05); }
}

@keyframes cakeGlow {
    0%, 100% {
        filter: drop-shadow(0 8px 16px rgba(255, 107, 157, 0.3));
    }
    50% {
        filter: drop-shadow(0 12px 24px rgba(255, 107, 157, 0.5));
    }
}

.cake-container:hover .birthday-cake-image {
    transform: translateY(-5px) scale(1.08);
}

.cake-container:hover .cake-img {
    filter: drop-shadow(0 6px 18px rgba(255, 20, 147, 0.5));
}

.cake-container:hover {
    filter: drop-shadow(0 8px 20px rgba(255, 20, 147, 0.4));
}

@keyframes buttonFloat {
    0% { opacity: 0; transform: translateY(30px); }
    100% { opacity: 1; transform: translateY(0); }
}

/* Kawaii hearts around title */
.kawaii-hearts {
    position: absolute;
    top: -10px;
    right: -30px;
    display: flex;
    gap: 8px;
}

.kawaii-hearts span {
    font-size: 1.5rem;
    animation: heartBeat 2s ease-in-out infinite;
}

.heart-1 { animation-delay: 0s; }
.heart-2 { animation-delay: 0.3s; }
.heart-3 { animation-delay: 0.6s; }

@keyframes heartBeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* Corner decorations */
.cute-corner-decorations {
    position: absolute;
    bottom: -20px;
    left: -20px;
    display: flex;
    gap: 15px;
}

.corner-sticker {
    font-size: 2rem;
    animation: stickerBounce 3s ease-in-out infinite;
    opacity: 0.9;
}

.sticker-1 { animation-delay: 0s; }
.sticker-2 { animation-delay: 1s; }
.sticker-3 { animation-delay: 2s; }

@keyframes stickerBounce {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-15px) rotate(10deg); }
}

/* Smiley face */
.smiley-face {
    font-size: 3rem;
    animation: smileFloat 3s ease-in-out infinite;
    margin-top: 20px;
}

@keyframes smileFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

/* Right section styling */
.right-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 35px;
    position: relative;
    padding-right: 60px;
    animation: slideInRight 1.2s ease-out 0.3s both;
}

@keyframes slideInRight {
    0% { opacity: 0; transform: translateX(100px); }
    100% { opacity: 1; transform: translateX(0); }
}

.photo-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.photo-circle {
    width: 320px;
    height: 320px;
    border-radius: 50%;
    background: linear-gradient(135deg, #fff 0%, #fce4ec 100%);
    padding: 10px;
    box-shadow: 0 20px 50px rgba(255, 107, 157, 0.25);
    animation: photoAppear 1.5s ease-out 0.6s both;
    position: relative;
    overflow: hidden;
}

.photo-circle::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #ff6b9d, #ff8fab, #ffa8cc, #ff6b9d);
    border-radius: 50%;
    z-index: -1;
    animation: borderRotate 3s linear infinite;
}

@keyframes borderRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.profile-photo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    display: block;
}

@keyframes photoAppear {
    0% { opacity: 0; transform: scale(0.5) rotate(-180deg); }
    100% { opacity: 1; transform: scale(1) rotate(0deg); }
}

/* Floating balloons */
.floating-balloons {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.balloon {
    position: absolute;
    font-size: 2.5rem;
    animation: balloonFloat 4s ease-in-out infinite;
}

.balloon-1 {
    top: -20px;
    right: -30px;
    color: #ff6b9d;
    animation-delay: 0s;
}

.balloon-2 {
    top: 50px;
    right: -50px;
    color: #ff8fab;
    animation-delay: 1s;
}

.balloon-3 {
    bottom: -20px;
    right: -40px;
    color: #ffa8cc;
    animation-delay: 2s;
}

@keyframes balloonFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    25% { transform: translateY(-15px) rotate(2deg); }
    50% { transform: translateY(-10px) rotate(-1deg); }
    75% { transform: translateY(-20px) rotate(3deg); }
}

/* Name label */
.name-label {
    background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%);
    padding: 12px 25px;
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(255, 107, 157, 0.3);
    animation: labelSlide 1s ease-out 1.4s both;
}

.name-text {
    font-family: 'Quicksand', sans-serif;
    font-size: 1.4rem;
    font-weight: 700;
    color: #fff;
    letter-spacing: 1px;
}

@keyframes labelSlide {
    0% { opacity: 0; transform: translateY(30px); }
    100% { opacity: 1; transform: translateY(0); }
}

/* Decorative elements */
.decorative-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
    top: 0;
    left: 0;
}

.star {
    position: absolute;
    font-size: 1.8rem;
    color: #ff6b9d;
    animation: starTwinkle 2s ease-in-out infinite;
}

.star-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.star-2 {
    bottom: 30%;
    right: 15%;
    animation-delay: 1s;
}

.circle-decoration {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, #ff8fab 0%, #ffa8cc 100%);
    opacity: 0.6;
    animation: circleFloat 3s ease-in-out infinite;
}

.circle-1 {
    width: 60px;
    height: 60px;
    top: 10%;
    right: 20%;
    animation-delay: 0.5s;
}

.circle-2 {
    width: 40px;
    height: 40px;
    bottom: 20%;
    left: 10%;
    animation-delay: 1.5s;
}

.circle-3 {
    width: 80px;
    height: 80px;
    top: 60%;
    right: 5%;
    animation-delay: 2.5s;
}

/* Additional kawaii elements */
.kawaii-element {
    position: absolute;
    font-size: 2.2rem;
    animation: kawaiiBounce 4s ease-in-out infinite;
    opacity: 0.8;
}

.element-1 {
    top: 5%;
    left: 25%;
    animation-delay: 0s;
}

.element-2 {
    bottom: 10%;
    right: 30%;
    animation-delay: 1s;
}

.element-3 {
    top: 35%;
    left: 5%;
    animation-delay: 2s;
}

.element-4 {
    bottom: 40%;
    right: 10%;
    animation-delay: 3s;
}

@keyframes kawaiiBounce {
    0%, 100% {
        transform: translateY(0) scale(1) rotate(0deg);
        opacity: 0.8;
    }
    25% {
        transform: translateY(-12px) scale(1.1) rotate(5deg);
        opacity: 1;
    }
    50% {
        transform: translateY(-8px) scale(0.95) rotate(-3deg);
        opacity: 0.9;
    }
    75% {
        transform: translateY(-15px) scale(1.05) rotate(8deg);
        opacity: 1;
    }
}

@keyframes circleFloat {
    0%, 100% { transform: translateY(0) scale(1); opacity: 0.6; }
    50% { transform: translateY(-20px) scale(1.1); opacity: 0.8; }
}

/* Cake effect animations */
@keyframes overlayFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes realisticBubbleFloat {
    0% {
        transform: translateY(0) translateX(0) scale(0.3);
        opacity: 0;
    }
    5% {
        opacity: 0.8;
        transform: translateY(-50px) translateX(0) scale(0.6);
    }
    15% {
        opacity: 1;
        transform: translateY(-150px) translateX(calc(var(--drift-x) * 0.2)) scale(0.9);
    }
    85% {
        opacity: 1;
        transform: translateY(-85vh) translateX(calc(var(--drift-x) * 0.8)) scale(1);
    }
    100% {
        transform: translateY(-100vh) translateX(var(--drift-x)) scale(1.1);
        opacity: 0;
    }
}

@keyframes petalFall {
    0% {
        transform: translateY(0) translateX(0) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(120vh) translateX(var(--drift, 0px)) rotate(720deg);
        opacity: 0;
    }
}

@keyframes image3DFloat {
    0% {
        opacity: 0;
        transform: perspective(1000px) rotateX(0deg) rotateY(0deg) scale(0) translateZ(0px);
    }
    15% {
        opacity: 1;
        transform: perspective(1000px) rotateX(10deg) rotateY(10deg) scale(1.1) translateZ(50px);
    }
    50% {
        opacity: 1;
        transform: perspective(1000px) rotateX(-5deg) rotateY(15deg) scale(1) translateZ(30px);
    }
    85% {
        opacity: 1;
        transform: perspective(1000px) rotateX(5deg) rotateY(-10deg) scale(0.9) translateZ(20px);
    }
    100% {
        opacity: 0;
        transform: perspective(1000px) rotateX(0deg) rotateY(0deg) scale(0.3) translateZ(0px);
    }
}

@keyframes sparkleFloat {
    0% {
        opacity: 0;
        transform: scale(0) rotate(0deg);
    }
    20% {
        opacity: 1;
        transform: scale(1.2) rotate(90deg);
    }
    80% {
        opacity: 1;
        transform: scale(1) rotate(270deg);
    }
    100% {
        opacity: 0;
        transform: scale(0) rotate(360deg);
    }
}

@keyframes heartFloat {
    0% {
        opacity: 0;
        transform: translateY(0) translateX(0) scale(0);
    }
    10% {
        opacity: 1;
        transform: translateY(-100px) translateX(var(--drift, 0px)) scale(1);
    }
    90% {
        opacity: 1;
        transform: translateY(-80vh) translateX(var(--drift, 0px)) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-100vh) translateX(var(--drift, 0px)) scale(0);
    }
}

@keyframes glowPulse {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

.image-bubble {
    --end-x: 0px;
}

.image-bubble:nth-child(odd) {
    --end-x: 100px;
}

.image-bubble:nth-child(even) {
    --end-x: -100px;
}

/* Close button animation */
@keyframes closeButtonPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 6px 20px rgba(255, 107, 157, 0.6);
    }
}

/* Removed paper airplane animations for better performance */

/* Optimized Letter Modal Styles */
.letter-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: letterModalFadeIn 0.5s ease-out;
    will-change: opacity;
    transform: translateZ(0);
}

@keyframes letterModalFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes letterModalFadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

.letter-container {
    position: relative;
    width: 700px;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Enhanced Letter Paper with Better Contrast and Visual Appeal */
.letter-paper {
    width: 100%;
    max-width: 600px;
    background: linear-gradient(135deg,
        #ffffff 0%,
        #fefefe 25%,
        #fff8fa 50%,
        #fefefe 75%,
        #ffffff 100%);
    border-radius: 20px;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.15),
        0 0 0 2px rgba(255, 182, 193, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.9),
        inset 0 -1px 0 rgba(255, 182, 193, 0.1),
        0 0 30px rgba(255, 107, 157, 0.1);
    padding: 50px 40px;
    position: relative;
    overflow: hidden;
    animation: enhancedLetterAppear 0.6s ease-out;
    border: 1px solid rgba(255, 182, 193, 0.2);
    will-change: transform, opacity, filter;
    transform: translateZ(0);
}

/* Enhanced paper texture overlay with gentle sparkles */
.letter-paper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 182, 193, 0.04) 0%, transparent 40%),
        radial-gradient(circle at 80% 20%, rgba(255, 107, 157, 0.04) 0%, transparent 40%),
        radial-gradient(circle at 40% 40%, rgba(255, 240, 245, 0.06) 0%, transparent 35%),
        radial-gradient(circle at 70% 70%, rgba(255, 182, 193, 0.03) 0%, transparent 30%),
        radial-gradient(circle at 30% 30%, rgba(255, 107, 157, 0.02) 0%, transparent 25%);
    pointer-events: none;
    z-index: 1;
    animation: paperGlow 8s ease-in-out infinite;
}

@keyframes paperGlow {
    0%, 100% {
        opacity: 0.8;
    }
    50% {
        opacity: 1;
    }
}

@keyframes enhancedLetterAppear {
    0% {
        opacity: 0;
        transform: scale(0.9) translateY(20px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Enhanced Letter Header */
.letter-header {
    text-align: center;
    margin-bottom: 40px;
    padding-bottom: 25px;
    border-bottom: 3px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(90deg, transparent, #ff6b9d, #ff8fab, #ffa8cc, transparent) border-box;
    border-image: linear-gradient(90deg, transparent, #ff6b9d, #ff8fab, #ffa8cc, transparent) 1;
    position: relative;
    z-index: 2;
}

.letter-header::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 6px;
    background: linear-gradient(90deg, transparent, #ff6b9d, transparent);
    border-radius: 3px;
    animation: headerGlow 3s ease-in-out infinite;
}

@keyframes headerGlow {
    0%, 100% {
        opacity: 0.6;
        transform: translateX(-50%) scale(1);
    }
    50% {
        opacity: 1;
        transform: translateX(-50%) scale(1.1);
    }
}

.letter-title {
    font-family: 'Pacifico', cursive;
    font-size: 2.8rem;
    background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 25%, #ffa8cc 50%, #ffb3d1 75%, #ff6b9d 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 15px;
    text-shadow: 0 4px 8px rgba(255, 107, 157, 0.3);
    animation: titleShimmer 4s ease-in-out infinite;
    position: relative;
    z-index: 2;
}

@keyframes titleShimmer {
    0%, 100% {
        background-position: 0% 50%;
        transform: scale(1);
    }
    50% {
        background-position: 100% 50%;
        transform: scale(1.02);
    }
}

.letter-subtitle {
    font-family: 'Dancing Script', cursive;
    font-size: 1.5rem;
    color: #666;
    font-weight: 600;
}

/* Enhanced Letter Content Area */
.letter-content {
    flex: 1;
    margin-bottom: 40px;
    line-height: 1.8;
    position: relative;
    overflow: hidden;
}

/* Floating sparkles in letter content */
.letter-content::before {
    content: '✨';
    position: absolute;
    top: 10%;
    right: 8%;
    font-size: 0.9rem;
    opacity: 0.7;
    animation: floatSparkle 4s ease-in-out infinite;
    z-index: 1;
    pointer-events: none;
}

.letter-content::after {
    content: '💫';
    position: absolute;
    bottom: 15%;
    left: 5%;
    font-size: 0.8rem;
    opacity: 0.6;
    animation: floatSparkle 5s ease-in-out infinite;
    animation-delay: 2s;
    z-index: 1;
    pointer-events: none;
}

@keyframes floatSparkle {
    0%, 100% {
        transform: translateY(0) rotate(0deg) scale(1);
        opacity: 0.6;
    }
    25% {
        transform: translateY(-8px) rotate(90deg) scale(1.1);
        opacity: 0.8;
    }
    50% {
        transform: translateY(-12px) rotate(180deg) scale(0.9);
        opacity: 1;
    }
    75% {
        transform: translateY(-6px) rotate(270deg) scale(1.05);
        opacity: 0.7;
    }
}

@keyframes textShimmer {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* Content Sparkles */
.content-sparkles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 3;
}

.content-sparkles .sparkle {
    position: absolute;
    font-size: 0.8rem;
    opacity: 0.7;
    animation: contentSparkleFloat 6s ease-in-out infinite;
}

.sparkle-1 {
    top: 20%;
    right: 15%;
    animation-delay: 0s;
    color: #ff6b9d;
}

.sparkle-2 {
    top: 60%;
    left: 10%;
    animation-delay: 2s;
    color: #ffa8cc;
}

.sparkle-3 {
    bottom: 25%;
    right: 20%;
    animation-delay: 4s;
    color: #ff8fab;
}

@keyframes contentSparkleFloat {
    0%, 100% {
        transform: translateY(0) scale(1) rotate(0deg);
        opacity: 0.5;
    }
    25% {
        transform: translateY(-10px) scale(1.2) rotate(90deg);
        opacity: 0.8;
    }
    50% {
        transform: translateY(-15px) scale(0.9) rotate(180deg);
        opacity: 1;
    }
    75% {
        transform: translateY(-8px) scale(1.1) rotate(270deg);
        opacity: 0.7;
    }
}

/* Gentle content glow animation */
@keyframes gentleContentGlow {
    0%, 100% {
        box-shadow:
            inset 0 1px 0 rgba(255, 255, 255, 0.6),
            0 4px 20px rgba(255, 107, 157, 0.1);
    }
    50% {
        box-shadow:
            inset 0 1px 0 rgba(255, 255, 255, 0.8),
            0 6px 25px rgba(255, 107, 157, 0.15),
            0 0 30px rgba(255, 182, 193, 0.1);
    }
}

/* Content floating elements */
.content-floating-element {
    position: absolute;
    font-size: 1rem;
    opacity: 0.6;
    pointer-events: none;
    color: #ff6b9d;
    filter: drop-shadow(0 2px 4px rgba(255, 107, 157, 0.3));
}

@keyframes contentElementFloat {
    0% {
        transform: translateX(0) translateY(0) scale(0.8);
        opacity: 0;
    }
    10% {
        opacity: 0.6;
    }
    50% {
        transform: translateX(40px) translateY(-20px) scale(1.1);
        opacity: 0.8;
    }
    90% {
        opacity: 0.4;
    }
    100% {
        transform: translateX(80px) translateY(-10px) scale(0.9);
        opacity: 0;
    }
}

/* Enhanced Letter Content with Elegant Typography and Visual Effects */
.typewriter-text {
    font-family: 'Crimson Text', serif;
    font-size: 1.4rem;
    color: #2c3e50;
    line-height: 1.8;
    min-height: 300px;
    font-weight: 400;
    text-align: justify;
    position: relative;
    z-index: 2;
    text-shadow:
        0 1px 2px rgba(255, 255, 255, 0.9),
        0 2px 4px rgba(44, 62, 80, 0.1);
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.4) 0%,
        rgba(255, 240, 245, 0.3) 50%,
        rgba(255, 255, 255, 0.4) 100%);
    padding: 25px 30px;
    border-radius: 18px;
    backdrop-filter: blur(8px);
    border: 2px solid rgba(255, 182, 193, 0.25);
    box-shadow:
        inset 0 1px 0 rgba(255, 255, 255, 0.6),
        0 4px 20px rgba(255, 107, 157, 0.1);
    overflow: hidden;
}

/* Subtle sparkle effects for letter content */
.typewriter-text::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 15% 25%, rgba(255, 107, 157, 0.08) 0%, transparent 30%),
        radial-gradient(circle at 85% 75%, rgba(255, 182, 193, 0.06) 0%, transparent 25%),
        radial-gradient(circle at 50% 50%, rgba(255, 240, 245, 0.1) 0%, transparent 40%);
    pointer-events: none;
    z-index: -1;
    animation: gentleSparkle 8s ease-in-out infinite;
}

@keyframes gentleSparkle {
    0%, 100% {
        opacity: 0.6;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.02);
    }
}

/* Enhanced text with gradient color */
.typewriter-text.enhanced {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 25%, #2c3e50 50%, #34495e 75%, #2c3e50 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 100%;
    animation: textShimmer 6s ease-in-out infinite;
}

/* Simple typing cursor */
.typing-cursor {
    display: inline;
    color: #ff6b9d;
    animation: typingCursorBlink 1s infinite;
    font-weight: bold;
}

@keyframes typingCursorBlink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* Handwritten signature animation */
@keyframes handwrittenAppear {
    0% {
        opacity: 0;
        transform: translateX(-20px) scale(0.9);
        filter: blur(2px);
    }
    100% {
        opacity: 1;
        transform: translateX(0) scale(1);
        filter: blur(0);
    }
}

/* Visual content for letter */
.letter-visual-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    gap: 30px;
}

.letter-hearts {
    display: flex;
    flex-direction: column;
    gap: 20px;
    animation: heartPulse 2s ease-in-out infinite;
}

.heart-row {
    font-size: 2.5rem;
    text-align: center;
    animation: heartFloat 3s ease-in-out infinite;
}

.heart-row:nth-child(2) {
    animation-delay: 0.5s;
}

.heart-row:nth-child(3) {
    animation-delay: 1s;
}

.letter-sparkles {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
}

.sparkle {
    font-size: 1.5rem;
    animation: sparkleRotate 2s linear infinite;
}

.sparkle:nth-child(2) { animation-delay: 0.4s; }
.sparkle:nth-child(3) { animation-delay: 0.8s; }
.sparkle:nth-child(4) { animation-delay: 1.2s; }
.sparkle:nth-child(5) { animation-delay: 1.6s; }

@keyframes heartPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes heartFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

@keyframes sparkleRotate {
    0% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.2); }
    100% { transform: rotate(360deg) scale(1); }
}

/* Enhanced Letter Signature */
.letter-signature {
    text-align: center;
    padding-top: 30px;
    border-top: 2px solid rgba(255, 107, 157, 0.2);
    position: relative;
    z-index: 2;
    margin-top: 20px;
}

.letter-signature::before {
    content: '';
    position: absolute;
    top: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, transparent, #ff6b9d, transparent);
    border-radius: 1px;
}

.signature-line {
    font-family: 'Dancing Script', cursive;
    color: #d63384;
    margin-bottom: 15px;
    font-style: italic;
    font-size: 1.5rem;
    font-weight: 700;
    opacity: 0;
    transform: translateX(-20px);
    text-shadow:
        0 1px 2px rgba(255, 255, 255, 0.8),
        0 2px 4px rgba(214, 51, 132, 0.4),
        0 0 10px rgba(255, 107, 157, 0.3);
}

.signature-name {
    font-family: 'Pacifico', cursive;
    font-size: 2.2rem;
    font-weight: 700;
    opacity: 0;
    transform: translateX(-20px);
    background: linear-gradient(135deg, #d63384 0%, #ff6b9d 25%, #ff8fab 50%, #ffa8cc 75%, #d63384 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow:
        0 2px 4px rgba(255, 255, 255, 0.8),
        0 3px 6px rgba(214, 51, 132, 0.4);
    position: relative;
}

.signature-name::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 60%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #ff6b9d, transparent);
    border-radius: 1px;
    opacity: 0.6;
}

/* Enhanced Letter Decorations */
.letter-decorations {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 3;
}

.decoration-heart {
    position: absolute;
    top: 20px;
    right: 30px;
    font-size: 1.5rem;
    animation: enhancedDecorationFloat 4s ease-in-out infinite;
    filter: drop-shadow(0 2px 4px rgba(255, 107, 157, 0.3));
}

.decoration-star {
    position: absolute;
    bottom: 30px;
    left: 30px;
    font-size: 1.3rem;
    animation: enhancedDecorationFloat 4s ease-in-out infinite;
    animation-delay: 1s;
    filter: drop-shadow(0 2px 4px rgba(255, 215, 0, 0.3));
}

.decoration-flower {
    position: absolute;
    top: 50%;
    right: 20px;
    font-size: 1.2rem;
    animation: enhancedDecorationFloat 4s ease-in-out infinite;
    animation-delay: 2s;
    filter: drop-shadow(0 2px 4px rgba(255, 182, 193, 0.3));
}

.decoration-butterfly {
    position: absolute;
    top: 30%;
    left: 25px;
    font-size: 1.4rem;
    animation: butterflyFly 6s ease-in-out infinite;
    animation-delay: 0.5s;
    filter: drop-shadow(0 2px 4px rgba(138, 43, 226, 0.3));
}

.decoration-sparkle {
    position: absolute;
    bottom: 20%;
    right: 40%;
    font-size: 1.1rem;
    animation: sparkleRotate 3s linear infinite;
    animation-delay: 1.5s;
    filter: drop-shadow(0 2px 4px rgba(255, 107, 157, 0.4));
}

@keyframes enhancedDecorationFloat {
    0%, 100% {
        transform: translateY(0) rotate(0deg) scale(1);
        opacity: 0.8;
    }
    25% {
        transform: translateY(-8px) rotate(3deg) scale(1.05);
        opacity: 1;
    }
    50% {
        transform: translateY(-12px) rotate(-2deg) scale(1.1);
        opacity: 0.9;
    }
    75% {
        transform: translateY(-6px) rotate(4deg) scale(1.05);
        opacity: 1;
    }
}

@keyframes butterflyFly {
    0%, 100% {
        transform: translateX(0) translateY(0) rotate(0deg);
    }
    25% {
        transform: translateX(15px) translateY(-10px) rotate(5deg);
    }
    50% {
        transform: translateX(30px) translateY(-5px) rotate(-3deg);
    }
    75% {
        transform: translateX(15px) translateY(-15px) rotate(7deg);
    }
}

/* Simple Floating Hearts */
.simple-floating-heart {
    position: fixed;
    font-size: 1.5rem;
    opacity: 0.8;
    pointer-events: none;
    filter: drop-shadow(0 2px 4px rgba(255, 107, 157, 0.3));
}

@keyframes simpleHeartFloat {
    0% {
        opacity: 0;
        transform: scale(0.8) translateY(0);
    }
    50% {
        opacity: 1;
        transform: scale(1.1) translateY(-20px);
    }
    100% {
        opacity: 0;
        transform: scale(0.8) translateY(-40px);
    }
}

/* Removed complex background effects for better performance */

/* Enhanced Letter Close Button */
.letter-close-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 55px;
    height: 55px;
    background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 50%, #ffa8cc 100%);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    color: white;
    font-size: 1.6rem;
    cursor: pointer;
    z-index: 1002;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow:
        0 6px 20px rgba(255, 107, 157, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    animation: closeButtonPulse 3s ease-in-out infinite;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.letter-close-btn:hover {
    transform: scale(1.15) rotate(90deg);
    box-shadow:
        0 8px 25px rgba(255, 107, 157, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    background: linear-gradient(135deg, #ff8fab 0%, #ffa8cc 50%, #ffb3d1 100%);
    animation: none;
}

.letter-close-btn:active {
    transform: scale(1.05) rotate(90deg);
    transition: all 0.1s ease;
}

@keyframes closeButtonPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow:
            0 6px 20px rgba(255, 107, 157, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow:
            0 8px 25px rgba(255, 107, 157, 0.5),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }
}

/* Performance optimization for reduced motion preference */
@media (prefers-reduced-motion: reduce) {
    .paper-airplane,
    .magical-particle,
    .floating-petal,
    .floating-heart,
    .handwritten-char {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
    }

    .letter-modal {
        backdrop-filter: none;
    }

    .letter-paper {
        animation: none;
        transform: none;
    }
}

/* Enhanced responsive design for letter */
@media (max-width: 768px) {
    .letter-container {
        width: 95vw;
        padding: 0 10px;
    }

    .letter-paper {
        padding: 30px 25px;
    }

    .letter-title {
        font-size: 2rem;
    }

    .letter-subtitle {
        font-size: 1.2rem;
    }

    .typewriter-text {
        font-size: 1.2rem;
        padding: 20px 25px;
        line-height: 1.7;
    }

    .signature-name {
        font-size: 1.5rem;
    }

    .content-sparkles .sparkle {
        font-size: 0.7rem;
    }

    .content-floating-element {
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .typewriter-text {
        font-size: 1.1rem;
        padding: 18px 20px;
        line-height: 1.6;
    }

    .letter-content::before,
    .letter-content::after {
        font-size: 0.6rem;
    }
}

/* Cute floating elements */
.cute-floating-elements {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 3;
}

.floating-emoji {
    position: absolute;
    font-size: 2rem;
    animation: floatAround 8s ease-in-out infinite;
    opacity: 0.8;
}

.emoji-1 {
    top: 15%;
    left: 8%;
    animation-delay: 0s;
    color: #ff6b9d;
}

.emoji-2 {
    top: 25%;
    right: 12%;
    animation-delay: 1s;
    color: #ffa8cc;
}

.emoji-3 {
    bottom: 30%;
    left: 10%;
    animation-delay: 2s;
    color: #ff8fab;
}

.emoji-4 {
    top: 60%;
    right: 8%;
    animation-delay: 3s;
    color: #ffb6d9;
}

.emoji-5 {
    bottom: 15%;
    right: 20%;
    animation-delay: 4s;
    color: #ff6b9d;
}

.emoji-6 {
    top: 40%;
    left: 5%;
    animation-delay: 5s;
    color: #ffa8cc;
}

.emoji-7 {
    bottom: 50%;
    right: 5%;
    animation-delay: 6s;
    color: #ff8fab;
}

.emoji-8 {
    top: 80%;
    left: 15%;
    animation-delay: 7s;
    color: #ffb6d9;
}

.emoji-9 {
    top: 35%;
    right: 25%;
    animation-delay: 8s;
    color: #ff6b9d;
}

.emoji-10 {
    bottom: 25%;
    left: 25%;
    animation-delay: 9s;
    color: #ffa8cc;
}

.emoji-11 {
    top: 70%;
    left: 35%;
    animation-delay: 10s;
    color: #ff8fab;
}

.emoji-12 {
    bottom: 60%;
    right: 35%;
    animation-delay: 11s;
    color: #ffb6d9;
}

@keyframes floatAround {
    0%, 100% {
        transform: translateY(0) translateX(0) rotate(0deg) scale(1);
        opacity: 0.8;
    }
    25% {
        transform: translateY(-20px) translateX(10px) rotate(5deg) scale(1.1);
        opacity: 1;
    }
    50% {
        transform: translateY(-10px) translateX(-15px) rotate(-3deg) scale(0.9);
        opacity: 0.9;
    }
    75% {
        transform: translateY(-25px) translateX(8px) rotate(8deg) scale(1.05);
        opacity: 1;
    }
}

/* Magical sparkles */
.magical-sparkles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 4;
}

.magic-sparkle {
    position: absolute;
    font-size: 1.5rem;
    animation: magicalFloat 6s ease-in-out infinite;
    opacity: 0.9;
}

.sparkle-a {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.sparkle-b {
    top: 60%;
    right: 20%;
    animation-delay: 1.2s;
}

.sparkle-c {
    bottom: 30%;
    left: 30%;
    animation-delay: 2.4s;
}

.sparkle-d {
    top: 40%;
    right: 40%;
    animation-delay: 3.6s;
}

.sparkle-e {
    bottom: 60%;
    right: 60%;
    animation-delay: 4.8s;
}

@keyframes magicalFloat {
    0%, 100% {
        transform: translateY(0) scale(1) rotate(0deg);
        opacity: 0.9;
    }
    25% {
        transform: translateY(-30px) scale(1.3) rotate(90deg);
        opacity: 1;
    }
    50% {
        transform: translateY(-15px) scale(0.8) rotate(180deg);
        opacity: 0.7;
    }
    75% {
        transform: translateY(-40px) scale(1.2) rotate(270deg);
        opacity: 1;
    }
}

/* Enhanced color harmony */
body {
    background: linear-gradient(135deg,
        #fef7f0 0%,
        #fce4ec 25%,
        #f8bbd9 50%,
        #fce4ec 75%,
        #fef7f0 100%);
    background-size: 400% 400%;
    animation: gradientMove 15s ease-in-out infinite;
}

/* Improved button interactions */
.interactive-buttons {
    position: relative;
}

.interactive-buttons::before {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: radial-gradient(circle, rgba(255, 107, 157, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: buttonAura 3s ease-in-out infinite;
    z-index: -1;
}

@keyframes buttonAura {
    0%, 100% {
        transform: scale(1);
        opacity: 0.5;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
}

/* Ảnh cute */
.photo-section {
    text-align: center;
}

.photo-frame {
    position: relative;
    display: inline-block;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    animation: photoFloat 3s ease-in-out infinite;
}

@keyframes photoFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

.birthday-photo {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: 20px;
    display: block;
}

.photo-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.sparkle {
    position: absolute;
    font-size: 1.2rem;
    animation: sparkleFloat 2s ease-in-out infinite;
}

.sparkle-1 {
    top: 10px;
    right: 10px;
    animation-delay: 0s;
}

.sparkle-2 {
    bottom: 10px;
    left: 10px;
    animation-delay: 0.7s;
}

.sparkle-3 {
    top: 50%;
    right: 10px;
    animation-delay: 1.4s;
}

@keyframes sparkleFloat {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.8; }
    50% { transform: scale(1.2) rotate(180deg); opacity: 1; }
}

/* Tiêu đề với hiệu ứng typewriter */
.birthday-title {
    font-family: 'Dancing Script', cursive;
    font-size: 2.2rem;
    color: #ff7eb3;
    text-align: center;
    line-height: 1.2;
    text-shadow: 0 0 16px #ffe0f7, 0 2px 8px #ff7eb3, 0 0 32px #ffb6c1;
    animation: titleGlow 2s ease-in-out infinite alternate;
}

.title-line {
    display: block;
    overflow: hidden;
    white-space: nowrap;
    border-right: 3px solid #ff7eb3;
    animation: typewriter 3s steps(20) 0.5s forwards, blink 0.75s step-end infinite;
    opacity: 0;
}

.title-line:nth-child(2) {
    animation-delay: 2s, 2.75s;
}

@keyframes typewriter {
    from { width: 0; opacity: 1; }
    to { width: 100%; opacity: 1; }
}

@keyframes blink {
    from, to { border-color: transparent; }
    50% { border-color: #ff7eb3; }
}

/* Icon bức thư */
.letter-section {
    text-align: center;
}

.letter-icon {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 18px;
    background: linear-gradient(135deg, #ffe0f7 0%, #fff6 100%);
    border-radius: 18px;
    border: 2px solid #ff7eb3;
    cursor: pointer;
    transition: all 0.3s ease;
    animation: letterPulse 2s ease-in-out infinite;
}

.letter-icon:hover {
    transform: scale(1.05) rotate(-2deg);
    box-shadow: 0 10px 25px rgba(255, 126, 179, 0.3);
    background: linear-gradient(135deg, #ff7eb3 0%, #ffe0f7 100%);
}

.letter-icon:hover i {
    color: #fff;
    transform: scale(1.2);
}

.letter-icon i {
    font-size: 2.5rem;
    color: #ff7eb3;
    transition: all 0.3s ease;
}

.letter-text {
    font-size: 0.9rem;
    color: #ff7eb3;
    font-weight: 600;
    transition: color 0.3s ease;
}

.letter-icon:hover .letter-text {
    color: #fff;
}

@keyframes letterPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

/* Hộp quà với hiệu ứng đẹp mắt */
.gift-section {
    text-align: center;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.gift-section:hover {
    transform: scale(1.05);
}

.gift-box {
    position: relative;
    width: 90px;
    height: 90px;
    margin: 0 auto 12px;
    animation: giftShake 2s ease-in-out infinite;
    cursor: pointer;
}

@keyframes giftShake {
    0%, 100% { transform: rotate(0deg) scale(1); }
    25% { transform: rotate(-3deg) scale(1.05); }
    75% { transform: rotate(3deg) scale(1.05); }
}

.gift-body {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
    border-radius: 15px;
    position: relative;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
    transition: all 0.3s ease;
}

.gift-box:hover .gift-body {
    box-shadow: 0 12px 35px rgba(255, 107, 107, 0.5);
    transform: translateY(-5px);
}

.gift-body::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 4px;
    background: #fff;
    transform: translateY(-50%);
}

.gift-body::after {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    bottom: 0;
    width: 4px;
    background: #fff;
    transform: translateX(-50%);
}

.gift-lid {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 18px;
    background: linear-gradient(45deg, #ff8e8e, #ff6b6b);
    border-radius: 8px;
    transition: transform 0.5s ease;
}

.gift-bow {
    position: absolute;
    top: -6px;
    left: 50%;
    transform: translateX(-50%);
    width: 22px;
    height: 22px;
    background: #ffd700;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
}

.gift-bow::before,
.gift-bow::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    background: #ffd700;
    border-radius: 50%;
    box-shadow: 0 3px 8px rgba(255, 215, 0, 0.4);
}

.gift-bow::before {
    top: -5px;
    left: -5px;
}

.gift-bow::after {
    top: -5px;
    right: -5px;
}

.gift-text {
    font-size: 1rem;
    color: #666;
    font-weight: 600;
    margin-top: 8px;
}

/* Lấp lánh ngôi sao */
.stars-bg {
    position: fixed;
    top: 0; left: 0; width: 100vw; height: 100vh;
    pointer-events: none;
    z-index: 0;
}

.star {
    position: absolute;
    width: 2.5px; height: 2.5px;
    background: #fff;
    border-radius: 50%;
    opacity: 0.7;
    animation: starTwinkle 2.5s infinite;
}

@keyframes starTwinkle {
    0%,100%{opacity:0.7}
    50%{opacity:1}
}

/* Confetti */
#confetti-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

.confetti {
    position: absolute;
    width: 10px;
    height: 10px;
    background: #ff6b6b;
    animation: confettiFall 3s linear forwards;
    border-radius: 50%;
    opacity: 0.85;
    filter: drop-shadow(0 2px 4px #fff8);
}

@keyframes confettiFall {
    0% {
        transform: translateY(-100px) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) rotate(720deg);
        opacity: 0;
    }
}

/* Enhanced entrance animations */
.birthday-layout {
    animation: layoutSlideIn 1.5s cubic-bezier(.68,-0.55,.27,1.55);
}

@keyframes layoutSlideIn {
    0% {
        opacity: 0;
        transform: translateY(100px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Staggered animation for cute elements */
.cute-floating-elements .floating-emoji {
    animation: floatAround 8s ease-in-out infinite, fadeInStagger 2s ease-out;
}

.emoji-1 { animation-delay: 0s, 0.2s; }
.emoji-2 { animation-delay: 1s, 0.4s; }
.emoji-3 { animation-delay: 2s, 0.6s; }
.emoji-4 { animation-delay: 3s, 0.8s; }
.emoji-5 { animation-delay: 4s, 1.0s; }
.emoji-6 { animation-delay: 5s, 1.2s; }
.emoji-7 { animation-delay: 6s, 1.4s; }
.emoji-8 { animation-delay: 7s, 1.6s; }

@keyframes fadeInStagger {
    0% {
        opacity: 0;
        transform: translateY(50px) scale(0.5);
    }
    100% {
        opacity: 0.8;
        transform: translateY(0) scale(1);
    }
}

/* Responsive Design */
@media (max-width: 900px) {
    .birthday-layout {
        flex-direction: column;
        gap: 40px;
        text-align: center;
    }

    .left-section, .right-section {
        padding: 0 20px;
    }

    .main-title {
        font-size: 4rem;
    }

    .photo-circle {
        width: 220px;
        height: 220px;
    }

    .floating-emoji {
        font-size: 1.5rem;
    }

    .kawaii-element {
        font-size: 1.8rem;
    }
}

@media (max-width: 600px) {
    .main-container {
        padding: 10px;
    }

    .birthday-layout {
        gap: 30px;
    }

    .main-title {
        font-size: 3rem;
    }

    .photo-circle {
        width: 180px;
        height: 180px;
    }

    .letter-button {
        font-size: 2.5rem;
    }

    .letter-img {
        width: 70px;
        height: 70px;
    }

    .letter-label {
        font-size: 0.8rem;
    }

    .birthday-cake-image {
        width: 70px;
        height: 70px;
    }

    .cake-bottom {
        width: 70px;
        height: 28px;
    }

    .cake-middle {
        width: 55px;
        height: 24px;
        left: 12px;
    }

    .cake-top {
        width: 40px;
        height: 20px;
        left: 20px;
    }

    .bunting-flags {
        height: 60px;
    }

    .flag {
        border-left-width: 20px;
        border-right-width: 20px;
        border-top-width: 30px;
    }

    .floating-emoji {
        font-size: 1.2rem;
    }

    .kawaii-element {
        font-size: 1.5rem;
    }

    .cute-corner-decorations {
        bottom: -10px;
        left: -10px;
        gap: 10px;
    }

    .corner-sticker {
        font-size: 1.5rem;
    }
}

.music-toggle {
    position: fixed;
    top: 24px;
    right: 24px;
    z-index: 1001;
    background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%);
    border: none;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    box-shadow: 0 4px 16px rgba(255, 122, 193, 0.25);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: box-shadow 0.3s, transform 0.2s;
    outline: none;
    font-size: 1.5rem;
}
.music-toggle:hover, .music-toggle.active {
    box-shadow: 0 0 24px 6px #ffb6c1, 0 4px 16px rgba(255, 122, 193, 0.25);
    transform: scale(1.08) rotate(-8deg);
    background: linear-gradient(135deg, #fad0c4 0%, #ff9a9e 100%);
}
.music-toggle i {
    color: #fff;
    text-shadow: 0 0 8px #ffb6c1, 0 2px 8px #ff7eb3;
    transition: color 0.2s;
}
.music-toggle.active i {
    color: #ff7eb3;
}
/* Hiệu ứng glow cho các nút, icon, khung ảnh */
.letter-icon, .gift-box, .photo-frame {
    transition: box-shadow 0.3s, transform 0.2s;
}
.letter-icon:hover, .gift-box:hover, .photo-frame:hover {
    box-shadow: 0 0 24px 6px #ffe0f7, 0 4px 16px rgba(255, 122, 193, 0.15);
    transform: scale(1.04) rotate(-2deg);
}

.auto-heart {
    will-change: transform, opacity;
    filter: drop-shadow(0 2px 8px #ffb6c1);
    user-select: none;
    animation: heartFloat 4s linear;
}
@keyframes heartFloat {
    0% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateY(-110vh) scale(1.2);
        opacity: 0;
    }
}

.gift-box:active, .letter-icon:active {
    animation: rippleClick 0.4s;
}
@keyframes rippleClick {
    0% {
        box-shadow: 0 0 0 0 #ffe0f7;
    }
    70% {
        box-shadow: 0 0 24px 16px #ffe0f7;
    }
    100% {
        box-shadow: 0 0 0 0 #ffe0f7;
    }
}

.flying-icon {
    will-change: transform, opacity;
    transition: all 1.2s cubic-bezier(.68,-0.55,.27,1.55);
    filter: drop-shadow(0 2px 8px #ffb6c1);
    user-select: none;
    pointer-events: none;
}

.flying-gift-img {
    will-change: transform, opacity;
    transition: all 1.3s cubic-bezier(.68,-0.55,.27,1.55);
    filter: drop-shadow(0 4px 16px #ffb6c1);
    user-select: none;
    pointer-events: none;
    opacity: 1;
    border-radius: 18px;
    box-shadow: 0 4px 16px rgba(255, 122, 193, 0.25);
}

.card-left, .card-right {
    flex: 1 1 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 0;
    min-height: 0;
}
.card-left {
    gap: 18px;
    padding: 32px 12px 32px 32px;
}
.card-right {
    gap: 22px;
    padding: 32px 32px 32px 12px;
}
.cute-stickers {
    display: flex;
    gap: 10px;
    margin-top: 18px;
    flex-wrap: wrap;
    justify-content: center;
}
.sticker-gif {
    width: 54px;
    height: 54px;
    object-fit: contain;
    border-radius: 12px;
    box-shadow: 0 2px 8px #ffe0f7;
    background: #fff8;
    transition: transform 0.2s;
    margin-bottom: 0;
}
.sticker-gif:hover {
    transform: scale(1.15) rotate(-6deg);
    box-shadow: 0 6px 18px #ffb6c1;
}
@media (max-width: 600px) {
    .card-left, .card-right {
        padding: 16px 6px;
    }
    .cute-stickers {
        gap: 6px;
        margin-top: 10px;
    }
    .sticker-gif {
        width: 38px;
        height: 38px;
    }
} 